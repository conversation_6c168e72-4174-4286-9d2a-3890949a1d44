package config

import (
	"fmt"

	"resumatter/pkg/logger"
)

// Config holds the application configuration
type Config struct {
	AI            AIConfig            `mapstructure:"ai"`
	Providers     ProviderConfig      `mapstructure:"providers"`
	Server        ServerConfig        `mapstructure:"server"`
	Auth          AuthConfig          `mapstructure:"auth"`
	Logging       LoggingConfig       `mapstructure:"logging"`
	Observability ObservabilityConfig `mapstructure:"observability"`
}

// AIConfig holds AI configuration
type AIConfig struct {
	DefaultProvider    string                       `mapstructure:"default_provider"`
	DefaultModel       string                       `mapstructure:"default_model"`
	DefaultTemperature float32                      `mapstructure:"default_temperature"`
	DefaultTimeout     string                       `mapstructure:"default_timeout"`
	Operations         map[string]OperationAIConfig `mapstructure:"operations"`
}

// OperationAIConfig holds operation-specific AI configuration
type OperationAIConfig struct {
	Provider       string               `mapstructure:"provider"`
	Model          string               `mapstructure:"model"`
	Temperature    float32              `mapstructure:"temperature"`
	MaxTokens      int                  `mapstructure:"max_tokens"`
	Timeout        string               `mapstructure:"timeout"`
	APIKey         string               `mapstructure:"api_key"`
	CircuitBreaker CircuitBreakerConfig `mapstructure:"circuit_breaker"`
	ProviderConfig map[string]any       `mapstructure:"provider_config"`
}

// ProviderConfig holds provider-specific configurations
type ProviderConfig struct {
	GeminiAPI GeminiAPIConfig `mapstructure:"genai-gemini"`
	VertexAI  VertexAIConfig  `mapstructure:"genai-vertexai"`
	OpenAI    OpenAIConfig    `mapstructure:"openai"`
	Anthropic AnthropicConfig `mapstructure:"anthropic"`
}

// GeminiAPIConfig holds Gemini API configuration
type GeminiAPIConfig struct {
	BaseURL string `mapstructure:"base_url"`
}

// VertexAIConfig holds Vertex AI configuration
type VertexAIConfig struct {
	GoogleCloudProject string `mapstructure:"google_cloud_project"`
	GoogleCloudRegion  string `mapstructure:"google_cloud_region"`
}

// OpenAIConfig holds OpenAI configuration
type OpenAIConfig struct {
	OrganizationID string `mapstructure:"organization_id"`
	BaseURL        string `mapstructure:"base_url"`
}

// AnthropicConfig holds Anthropic configuration
type AnthropicConfig struct {
	BaseURL string `mapstructure:"base_url"`
}

// ServerConfig holds HTTP server configuration
type ServerConfig struct {
	Port        string `mapstructure:"port"`
	Environment string `mapstructure:"environment"`
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	Enabled      bool          `mapstructure:"enabled"`
	BearerTokens []BearerToken `mapstructure:"bearer_tokens"`
	LoggingOnly  bool          `mapstructure:"logging_only"`
}

// BearerToken represents a bearer token with permissions
type BearerToken struct {
	Token       string   `mapstructure:"token"`
	Permissions []string `mapstructure:"permissions"`
}

// CircuitBreakerConfig holds circuit breaker configuration
type CircuitBreakerConfig struct {
	Enabled          bool    `mapstructure:"enabled"`
	FailureThreshold float64 `mapstructure:"failure_threshold"`
	MinRequests      uint32  `mapstructure:"min_requests"`
	MaxRequests      uint32  `mapstructure:"max_requests"`
	Interval         string  `mapstructure:"interval"`
	Timeout          string  `mapstructure:"timeout"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

// ObservabilityConfig holds observability configuration
type ObservabilityConfig struct {
	TracingEnabled bool   `mapstructure:"tracing_enabled"`
	ServiceName    string `mapstructure:"service_name"`
	ServiceVersion string `mapstructure:"service_version"`
}

// GetOperationConfig returns the configuration for a specific operation
func (c *Config) GetOperationConfig(operation OperationType) (*OperationAIConfig, error) {
	opConfig, exists := c.AI.Operations[string(operation)]
	if !exists {
		return nil, fmt.Errorf("no configuration found for operation: %s", operation)
	}

	// Apply defaults where not specified
	if opConfig.Provider == "" {
		opConfig.Provider = c.AI.DefaultProvider
	}
	if opConfig.Model == "" {
		opConfig.Model = c.AI.DefaultModel
	}
	if opConfig.Temperature == 0 {
		opConfig.Temperature = c.AI.DefaultTemperature
	}
	if opConfig.Timeout == "" {
		opConfig.Timeout = c.AI.DefaultTimeout
	}

	return &opConfig, nil
}

// ValidateOperationConfigs validates that all required operations are configured
func (c *Config) ValidateOperationConfigs() error {
	requiredOps := []OperationType{OperationTailor, OperationEvaluate, OperationAnalyze}

	for _, op := range requiredOps {
		if _, exists := c.AI.Operations[string(op)]; !exists {
			return fmt.Errorf("missing required operation configuration: %s", op)
		}
	}

	return nil
}

// Validate validates the entire configuration
func (c *Config) Validate() error {
	// Validate AI configuration
	if c.AI.DefaultProvider == "" {
		return fmt.Errorf("default AI provider is required")
	}
	if c.AI.DefaultModel == "" {
		return fmt.Errorf("default AI model is required")
	}

	// Validate operation configurations
	if err := c.ValidateOperationConfigs(); err != nil {
		return fmt.Errorf("operation config validation failed: %w", err)
	}

	// Validate server configuration
	if c.Server.Port == "" {
		return fmt.Errorf("server port cannot be empty")
	}

	// Validate auth configuration
	if c.Auth.Enabled && len(c.Auth.BearerTokens) == 0 {
		return fmt.Errorf("authentication is enabled but no bearer tokens are configured")
	}

	return nil
}

// ParseLogLevel parses the log level from string
func ParseLogLevel(level string) logger.Level {
	switch level {
	case "debug":
		return logger.LevelDebug
	case "info":
		return logger.LevelInfo
	case "warn", "warning":
		return logger.LevelWarn
	case "error":
		return logger.LevelError
	default:
		return logger.LevelInfo
	}
}

// ParseLogFormat parses the log format from string
func ParseLogFormat(format string) logger.Format {
	switch format {
	case "json":
		return logger.FormatJSON
	case "text":
		return logger.FormatText
	default:
		return logger.FormatText
	}
}
