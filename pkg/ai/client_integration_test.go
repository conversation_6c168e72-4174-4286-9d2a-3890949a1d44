//go:build integration

package ai

import (
	"context"
	"os"
	"strings"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/types"
)

// Helper function to create integration test configuration
func createIntegrationTestConfig(apiKey string) *config.Config {
	return &config.Config{
		AI: config.AIConfig{
			DefaultProvider:    "genai-gemini",
			DefaultModel:       "gemini-2.0-flash-lite",
			DefaultTemperature: 0.7,
			DefaultTimeout:     "30s",
			Operations: map[string]config.OperationAIConfig{
				"tailor": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash-lite",
					Temperature: 0.7,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      apiKey,
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disabled for integration tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
				"evaluate": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash-lite",
					Temperature: 0.3,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      apiKey,
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disabled for integration tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
				"analyze": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash-lite",
					Temperature: 0.5,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      apiKey,
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disabled for integration tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
			},
		},
		Providers: config.ProviderConfig{
			GeminiAPI: config.GeminiAPIConfig{},
		},
		Server: config.ServerConfig{
			Port:        "8080",
			Environment: "test",
		},
		Auth: config.AuthConfig{
			Enabled:     false,
			LoggingOnly: true,
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
		},
		Observability: config.ObservabilityConfig{
			TracingEnabled: false,
			ServiceName:    "resumatter-integration-test",
			ServiceVersion: "test",
		},
	}
}

// TestClient_TailorResume tests the TailorResume method with real API
func TestClient_TailorResume(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.Skip("Skipping TailorResume test: no API key available")
	}

	config := createIntegrationTestConfig(apiKey)
	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	input := types.TailorResumeInput{
		BaseResume: `John Doe
Software Engineer
5 years experience in Go, Python, and cloud technologies.
Experience with microservices, Docker, and Kubernetes.`,
		JobDescription: `We are looking for a Senior Software Engineer with experience in:
- Go programming language
- Cloud platforms (AWS, GCP)
- Microservices architecture
- Container technologies`,
	}

	ctx := context.Background()
	result, err := client.TailorResume(ctx, input)
	if err != nil {
		t.Fatalf("TailorResume failed: %v", err)
	}

	if result == nil {
		t.Fatal("TailorResume returned nil result")
	}

	if result.TailoredResume == "" {
		t.Error("TailoredResume is empty")
	}

	if result.ATSAnalysis.Score == 0 {
		t.Error("ATSAnalysis score is 0")
	}

	if result.ATSAnalysis.Strengths == "" {
		t.Error("ATSAnalysis strengths is empty")
	}

	if result.JobPostingAnalysis.Clarity == "" {
		t.Error("JobPostingAnalysis clarity is empty")
	}

	t.Logf("TailorResume completed successfully")
	t.Logf("ATS Score: %d", result.ATSAnalysis.Score)
	t.Logf("Tailored resume length: %d characters", len(result.TailoredResume))
}

// TestClient_EvaluateResume tests the EvaluateResume method with real API
func TestClient_EvaluateResume(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.Skip("Skipping EvaluateResume test: no API key available")
	}

	config := createIntegrationTestConfig(apiKey)
	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	input := types.EvaluateResumeInput{
		BaseResume: `John Doe
Software Engineer
5 years experience in Go, Python, and cloud technologies.`,
		TailoredResume: `John Doe
Senior Software Engineer
5 years experience in Go programming, Python development, and cloud technologies including AWS and GCP.
Extensive experience with microservices architecture, Docker containerization, and Kubernetes orchestration.`,
	}

	ctx := context.Background()
	result, err := client.EvaluateResume(ctx, input)
	if err != nil {
		t.Fatalf("EvaluateResume failed: %v", err)
	}

	if result == nil {
		t.Fatal("EvaluateResume returned nil result")
	}

	if result.Summary == "" {
		t.Error("Summary is empty")
	}

	if len(result.Findings) == 0 {
		t.Error("Findings is empty")
	}

	// Check that findings have required fields
	for i, finding := range result.Findings {
		if finding.Type == "" {
			t.Errorf("Finding %d has empty type", i)
		}
		if finding.Description == "" {
			t.Errorf("Finding %d has empty description", i)
		}
		if finding.Evidence == "" {
			t.Errorf("Finding %d has empty evidence", i)
		}
	}

	t.Logf("EvaluateResume completed successfully")
	t.Logf("Found %d findings", len(result.Findings))
	t.Logf("Summary length: %d characters", len(result.Summary))
}

// TestClient_AnalyzeJob tests the AnalyzeJob method with real API
func TestClient_AnalyzeJob(t *testing.T) {
	// Skip if no API key available
	apiKey := getAPIKey()
	if apiKey == "" {
		t.Skip("Skipping AnalyzeJob test: no API key available")
	}

	config := createIntegrationTestConfig(apiKey)
	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	input := types.AnalyzeJobInput{
		JobDescription: `Senior Software Engineer Position

We are seeking a highly skilled Senior Software Engineer to join our team.

Requirements:
- 5+ years of experience in software development
- Proficiency in Go, Python, or Java
- Experience with cloud platforms (AWS, GCP, Azure)
- Strong understanding of microservices architecture
- Experience with Docker and Kubernetes

Responsibilities:
- Design and implement scalable software solutions
- Collaborate with cross-functional teams
- Mentor junior developers
- Participate in code reviews

We offer competitive salary, excellent benefits, and flexible work arrangements.`,
	}

	ctx := context.Background()
	result, err := client.AnalyzeJob(ctx, input)
	if err != nil {
		t.Fatalf("AnalyzeJob failed: %v", err)
	}

	if result == nil {
		t.Fatal("AnalyzeJob returned nil result")
	}

	if result.JobQualityScore == 0 {
		t.Error("JobQualityScore is 0")
	}

	if result.Clarity.Score == 0 {
		t.Error("Clarity score is 0")
	}

	if result.Clarity.Analysis == "" {
		t.Error("Clarity analysis is empty")
	}

	if result.Inclusivity.Score == 0 {
		t.Error("Inclusivity score is 0")
	}

	if result.CandidateAttraction.Score == 0 {
		t.Error("CandidateAttraction score is 0")
	}

	if result.MarketCompetitiveness.SalaryTransparency == "" {
		t.Error("MarketCompetitiveness salary transparency is empty")
	}

	if len(result.Recommendations) == 0 {
		t.Error("Recommendations is empty")
	}

	t.Logf("AnalyzeJob completed successfully")
	t.Logf("Job Quality Score: %d", result.JobQualityScore)
	t.Logf("Clarity Score: %d", result.Clarity.Score)
	t.Logf("Inclusivity Score: %d", result.Inclusivity.Score)
	t.Logf("Found %d recommendations", len(result.Recommendations))
}

// getAPIKey retrieves the API key from environment or file
func getAPIKey() string {
	// Try environment variable first
	if key := os.Getenv("GEMINI_API_KEY"); key != "" {
		return key
	}

	// Try reading from .key/gemini file
	if data, err := os.ReadFile("./.key/gemini"); err == nil {
		key := strings.TrimSpace(string(data))
		if key != "" {
			return key
		}
	}

	return ""
}
