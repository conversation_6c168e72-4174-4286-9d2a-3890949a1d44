package providers

import (
	"context"
	"fmt"

	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// AnthropicProvider implements Provider interface for Anthropic
type AnthropicProvider struct {
	config *config.AnthropicConfig
	logger logger.Logger
}

// NewAnthropicProvider creates a new Anthropic provider
func NewAnthropicProvider(cfg *config.AnthropicConfig, logger logger.Logger) (*AnthropicProvider, error) {
	return &AnthropicProvider{
		config: cfg,
		logger: logger.Named("anthropic"),
	}, nil
}

// GenerateStructuredContent generates structured content using Anthropic
func (p *AnthropicProvider) GenerateStructuredContent(ctx context.Context, request *GenerationRequest) (*GenerationResponse, error) {
	return nil, fmt.Errorf("anthropic provider not yet implemented")
}

// Name returns the provider name
func (p *AnthropicProvider) Name() string {
	return "anthropic"
}

// SupportedModels returns the list of supported models
func (p *AnthropicProvider) SupportedModels() []string {
	return []string{}
}

// Close closes the provider
func (p *AnthropicProvider) Close() error {
	return nil
}
