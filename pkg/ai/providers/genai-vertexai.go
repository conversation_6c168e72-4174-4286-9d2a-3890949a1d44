package providers

import (
	"context"
	"fmt"

	"google.golang.org/genai"

	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// VertexAIProvider implements Provider interface for Vertex AI
type VertexAIProvider struct {
	client *genai.Client
	config *config.VertexAIConfig
	logger logger.Logger
}

// NewVertexAIProvider creates a new Vertex AI provider
func NewVertexAIProvider(cfg *config.VertexAIConfig, logger logger.Logger) (*VertexAIProvider, error) {
	if cfg.GoogleCloudProject == "" {
		return nil, fmt.Errorf("google_cloud_project is required for Vertex AI provider")
	}

	client, err := genai.NewClient(context.Background(), &genai.ClientConfig{
		Backend:  genai.BackendVertexAI,
		Project:  cfg.GoogleCloudProject,
		Location: cfg.GoogleCloudRegion,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Vertex AI client: %w", err)
	}

	return &VertexAIProvider{
		client: client,
		config: cfg,
		logger: logger.Named("genai-vertexai"),
	}, nil
}

// GenerateStructuredContent generates structured content using Vertex AI
func (p *VertexAIProvider) GenerateStructuredContent(ctx context.Context, request *GenerationRequest) (*GenerationResponse, error) {
	genaiConfig := &genai.GenerateContentConfig{
		ResponseMIMEType:  "application/json",
		Temperature:       &request.Config.Temperature,
		SystemInstruction: genai.NewContentFromText(request.SystemPrompt, genai.RoleUser),
	}

	// Set max tokens if specified
	if request.Config.MaxTokens > 0 {
		maxTokens := int32(request.Config.MaxTokens)
		genaiConfig.MaxOutputTokens = maxTokens
	}

	// Set response schema if provided
	if request.Schema != nil {
		genaiConfig.ResponseSchema = genaiConvertSchema(request.Schema)
	}

	// Create context with timeout
	timeoutCtx := ctx
	if request.Config.Timeout > 0 {
		var cancel context.CancelFunc
		timeoutCtx, cancel = context.WithTimeout(ctx, request.Config.Timeout)
		defer cancel()
	}

	result, err := p.client.Models.GenerateContent(
		timeoutCtx,
		request.Config.Model,
		genai.Text(request.UserPrompt),
		genaiConfig,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate content: %w", err)
	}

	// Extract response
	if len(result.Candidates) == 0 || result.Candidates[0].Content == nil || len(result.Candidates[0].Content.Parts) == 0 {
		return nil, fmt.Errorf("received empty response from Vertex AI")
	}

	response := &GenerationResponse{
		Content: result.Text(),
		Metadata: map[string]any{
			"model":    request.Config.Model,
			"provider": "genai-vertexai",
			"project":  p.config.GoogleCloudProject,
			"region":   p.config.GoogleCloudRegion,
		},
	}

	// Extract usage stats if available
	if result.UsageMetadata != nil {
		response.Usage = &UsageStats{
			PromptTokens:     int(result.UsageMetadata.PromptTokenCount),
			CompletionTokens: int(result.UsageMetadata.CandidatesTokenCount),
			TotalTokens:      int(result.UsageMetadata.TotalTokenCount),
		}
	}

	return response, nil
}

// Name returns the provider name
func (p *VertexAIProvider) Name() string {
	return "genai-vertexai"
}

// SupportedModels returns the list of supported models
func (p *VertexAIProvider) SupportedModels() []string {
	return []string{
		"gemini-2.0-flash",
		"gemini-2.0-flash-lite",
		"gemini-1.5-pro",
		"gemini-1.5-flash",
	}
}

// Close closes the provider
func (p *VertexAIProvider) Close() error {
	// genai.Client doesn't have a Close method in the current version
	return nil
}
