package providers

import (
	"context"
	"fmt"

	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// OpenAIProvider implements Provider interface for OpenAI
type OpenAIProvider struct {
	config *config.OpenAIConfig
	logger logger.Logger
}

// NewOpenAIProvider creates a new OpenAI provider
func NewOpenAIProvider(cfg *config.OpenAIConfig, logger logger.Logger) (*OpenAIProvider, error) {
	return &OpenAIProvider{
		config: cfg,
		logger: logger.Named("openai"),
	}, nil
}

// GenerateStructuredContent generates structured content using OpenAI
func (p *OpenAIProvider) GenerateStructuredContent(ctx context.Context, request *GenerationRequest) (*GenerationResponse, error) {
	return nil, fmt.Errorf("openai provider not yet implemented")
}

// Name returns the provider name
func (p *OpenAIProvider) Name() string {
	return "openai"
}

// SupportedModels returns the list of supported models
func (p *OpenAIProvider) SupportedModels() []string {
	return []string{}
}

// Close closes the provider
func (p *OpenAIProvider) Close() error {
	return nil
}
