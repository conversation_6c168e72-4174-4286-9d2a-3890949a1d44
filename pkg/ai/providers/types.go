package providers

import (
	"context"
	"time"
)

// Provider defines the interface for AI providers
type Provider interface {
	GenerateStructuredContent(ctx context.Context, request *GenerationRequest) (*GenerationResponse, error)
	Name() string
	SupportedModels() []string
	Close() error
}

// GenerationRequest standardizes input across providers
type GenerationRequest struct {
	SystemPrompt string
	UserPrompt   string
	Schema       *ResponseSchema
	Config       *GenerationConfig
}

// GenerationConfig holds generation parameters
type GenerationConfig struct {
	Model       string
	Temperature float32
	MaxTokens   int
	Timeout     time.Duration
}

// GenerationResponse standardizes output across providers
type GenerationResponse struct {
	Content  string
	Metadata map[string]any
	Usage    *UsageStats
}

// ResponseSchema defines expected JSON structure
type ResponseSchema struct {
	Type       string         `json:"type"`
	Properties map[string]any `json:"properties"`
	Required   []string       `json:"required"`
}

// UsageStats tracks token usage
type UsageStats struct {
	PromptTokens     int
	CompletionTokens int
	TotalTokens      int
}
