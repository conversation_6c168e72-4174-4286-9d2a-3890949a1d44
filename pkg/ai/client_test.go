//go:build fast

package ai

import (
	"context"
	"strings"
	"testing"

	"resumatter/pkg/config"
	"resumatter/pkg/types"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name    string
		config  *config.Config
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid config with all operations",
			config:  createValidTestConfig(),
			wantErr: false,
		},
		{
			name:    "nil config",
			config:  nil,
			wantErr: true,
			errMsg:  "config cannot be nil",
		},
		{
			name: "missing operation config",
			config: &config.Config{
				AI: config.AIConfig{
					DefaultProvider: "genai-gemini",
					DefaultModel:    "gemini-2.0-flash-lite",
					Operations: map[string]config.OperationAIConfig{
						"tailor": {Provider: "genai-gemini", Model: "test-model", APIKey: "test-key"},
						// Missing evaluate and analyze
					},
				},
				Providers: config.ProviderConfig{
					GeminiAPI: config.GeminiAPIConfig{BaseURL: "https://test.com"},
				},
				Server: config.ServerConfig{Port: "8080"},
				Auth:   config.AuthConfig{Enabled: false},
			},
			wantErr: true,
			errMsg:  "invalid operation configs",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client, err := New(tt.config)

			if tt.wantErr {
				if err == nil {
					t.Errorf("New() error = nil, wantErr %v", tt.wantErr)
					return
				}
				if tt.errMsg != "" && !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("New() error = %v, want error containing %v", err, tt.errMsg)
				}
				return
			}

			if err != nil {
				t.Errorf("New() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if client == nil {
				t.Error("New() returned nil client")
				return
			}

			if client.config != tt.config {
				t.Errorf("New() client.config = %v, want %v", client.config, tt.config)
			}

			// Verify operation configs are loaded
			if len(client.operationConfigs) != 3 {
				t.Errorf("Expected 3 operation configs, got %d", len(client.operationConfigs))
			}

			// Verify factory is created
			if client.factory == nil {
				t.Error("Provider factory not created")
			}
		})
	}
}

// Helper function to create a valid test configuration
func createValidTestConfig() *config.Config {
	return &config.Config{
		AI: config.AIConfig{
			DefaultProvider:    "genai-gemini",
			DefaultModel:       "gemini-2.0-flash-lite",
			DefaultTemperature: 0.7,
			DefaultTimeout:     "30s",
			Operations: map[string]config.OperationAIConfig{
				"tailor": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash",
					Temperature: 0.7,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disabled for tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
				"evaluate": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash-lite",
					Temperature: 0.3,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disabled for tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
				"analyze": {
					Provider:    "genai-gemini",
					Model:       "gemini-2.0-flash",
					Temperature: 0.5,
					MaxTokens:   8192,
					Timeout:     "30s",
					APIKey:      "test-key",
					CircuitBreaker: config.CircuitBreakerConfig{
						Enabled:          false, // Disabled for tests
						FailureThreshold: 0.6,
						MinRequests:      5,
						MaxRequests:      3,
						Interval:         "60s",
						Timeout:          "30s",
					},
				},
			},
		},
		Providers: config.ProviderConfig{
			GeminiAPI: config.GeminiAPIConfig{
				BaseURL: "https://generativelanguage.googleapis.com",
			},
		},
		Server: config.ServerConfig{
			Port:        "8080",
			Environment: "test",
		},
		Auth: config.AuthConfig{
			Enabled:     false,
			LoggingOnly: true,
		},
		Logging: config.LoggingConfig{
			Level:  "info",
			Format: "text",
		},
		Observability: config.ObservabilityConfig{
			TracingEnabled: false,
			ServiceName:    "resumatter-test",
			ServiceVersion: "test",
		},
	}
}

func TestBuildTailorSchema(t *testing.T) {
	config := createValidTestConfig()
	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	schema := client.buildTailorSchema()

	if schema == nil {
		t.Fatal("buildTailorSchema() returned nil")
	}

	if schema.Type != "object" {
		t.Errorf("buildTailorSchema() Type = %v, want %v", schema.Type, "object")
	}

	// Check required properties exist
	requiredProps := []string{"tailoredResume", "atsAnalysis", "jobPostingAnalysis"}
	for _, prop := range requiredProps {
		if _, exists := schema.Properties[prop]; !exists {
			t.Errorf("buildTailorSchema() missing required property: %s", prop)
		}
	}

	// Check required fields are listed
	if len(schema.Required) != 3 {
		t.Errorf("buildTailorSchema() Required length = %d, want 3", len(schema.Required))
	}
}

func TestBuildEvaluateSchema(t *testing.T) {
	config := createValidTestConfig()
	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	schema := client.buildEvaluateSchema()

	if schema == nil {
		t.Fatal("buildEvaluateSchema() returned nil")
	}

	if schema.Type != "object" {
		t.Errorf("buildEvaluateSchema() Type = %v, want %v", schema.Type, "object")
	}

	// Check required properties exist
	requiredProps := []string{"summary", "findings"}
	for _, prop := range requiredProps {
		if _, exists := schema.Properties[prop]; !exists {
			t.Errorf("buildEvaluateSchema() missing required property: %s", prop)
		}
	}
}

func TestBuildAnalyzeSchema(t *testing.T) {
	config := createValidTestConfig()
	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	schema := client.buildAnalyzeSchema()

	if schema == nil {
		t.Fatal("buildAnalyzeSchema() returned nil")
	}

	if schema.Type != "object" {
		t.Errorf("buildAnalyzeSchema() Type = %v, want %v", schema.Type, "object")
	}

	// Check required properties exist
	requiredProps := []string{"jobQualityScore", "clarity", "inclusivity", "candidateAttraction", "marketCompetitiveness", "recommendations"}
	for _, prop := range requiredProps {
		if _, exists := schema.Properties[prop]; !exists {
			t.Errorf("buildAnalyzeSchema() missing required property: %s", prop)
		}
	}

	// Check jobQualityScore is integer type
	if scoreSchema, exists := schema.Properties["jobQualityScore"]; exists {
		if scoreMap, ok := scoreSchema.(map[string]any); ok {
			if scoreType, exists := scoreMap["type"]; exists {
				if scoreType != "integer" {
					t.Errorf("buildAnalyzeSchema() jobQualityScore type = %v, want integer", scoreType)
				}
			}
		}
	}
}

func TestExecuteOperation(t *testing.T) {
	config := createValidTestConfig()
	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	// Test with invalid operation
	ctx := context.Background()
	var output types.TailorResumeOutput
	err = client.executeOperation(ctx, "invalid", "system", "user", nil, &output)
	if err == nil {
		t.Error("executeOperation() with invalid operation should return error")
	}
	if !strings.Contains(err.Error(), "no configuration found") {
		t.Errorf("executeOperation() error = %v, want error containing 'no configuration found'", err)
	}
}

func TestClose(t *testing.T) {
	config := createValidTestConfig()
	client, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	err = client.Close()
	if err != nil {
		t.Errorf("Close() error = %v, want nil", err)
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

// Note: API integration tests have been moved to client_integration_test.go
// These unit tests focus on testing the client without making actual API calls
