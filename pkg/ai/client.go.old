package ai

import (
	"context"
	"encoding/json"
	"fmt"

	"google.golang.org/genai"

	"resumatter/pkg/config"
	"resumatter/pkg/logger"
	"resumatter/pkg/types"
)

// Client handles AI operations using Google Gemini
type Client struct {
	client *genai.Client
	config *config.Config
	logger logger.Logger
}

// Make sure Client implements ClientInterface
var _ ClientInterface = (*Client)(nil)

// New creates a new AI client
func New(cfg *config.Config, opts ...ClientOption) (*Client, error) {
	ctx := context.Background()
	if cfg.UseVertexAI {
		// can use gcloud 'Application Default Credentials' for quick setup.
		// too complex to explain here.
		client, err := genai.NewClient(ctx, &genai.ClientConfig{
			Backend:  genai.BackendVertexAI,
			Project:  cfg.GoogleCloudProject,
			Location: cfg.GoogleCloudRegion,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to create Vertex AI client: %w", err)
		}

		return &Client{
			client: client,
			config: cfg,
		}, nil
	}

	// For regular API mode, we need an API key
	if cfg.APIKey == "" {
		return nil, fmt.Errorf("API key is required (set GEMINI_API_KEY or RESUMATTER_AI_APIKEY environment variable)")
	}

	client, err := genai.NewClient(ctx, &genai.ClientConfig{
		APIKey:  cfg.APIKey,
		Backend: genai.BackendGeminiAPI,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	c := &Client{
		client: client,
		config: cfg,
		logger: logger.NewDefault().Named("ai-client"),
	}

	// Apply options
	for _, opt := range opts {
		opt(c)
	}

	return c, nil
}

func (c *Client) generateJSON(ctx context.Context, systemPrompt, userPrompt string, schema *genai.Schema, output any, operationConfig *config.ResolvedOperationConfig, operationType config.OperationType) error {
	// Use operation-specific configuration if provided, otherwise fall back to client config
	temperature := c.config.Temperature
	model := c.config.Model

	if operationConfig != nil {
		temperature = operationConfig.Temperature
		model = operationConfig.Model
	}

	genaiConfig := &genai.GenerateContentConfig{
		ResponseMIMEType: "application/json",
		Temperature:      &temperature,
		// this is what the documentation says and exactly the example in the documentation shows.
		// https://ai.google.dev/gemini-api/docs/text-generation#system-instructions
		SystemInstruction: genai.NewContentFromText(systemPrompt, genai.RoleUser),
		ResponseSchema:    schema,
	}

	// Set max tokens if specified in operation config
	if operationConfig != nil && operationConfig.MaxTokens > 0 {
		maxTokens := int32(operationConfig.MaxTokens)
		genaiConfig.MaxOutputTokens = maxTokens
	}

	// Create circuit breaker for this operation
	circuitBreaker := NewAICircuitBreaker(operationType, operationConfig, c.logger)

	// Execute the AI call with circuit breaker protection
	result, err := circuitBreaker.Execute(func() (*genai.GenerateContentResponse, error) {
		return c.client.Models.GenerateContent(ctx, model, genai.Text(userPrompt), genaiConfig)
	})

	if err != nil {
		// this can be checked later in http handler with errors.Is(err, gobreaker.ErrOpenState) to return 503
		return fmt.Errorf("failed to generate content: %w", err)
	}

	// It's safer to access the first candidate's content.
	if len(result.Candidates) == 0 || result.Candidates[0].Content == nil || len(result.Candidates[0].Content.Parts) == 0 {
		return fmt.Errorf("received an empty response from the AI")
	}

	if err := json.Unmarshal([]byte(result.Text()), output); err != nil {
		return fmt.Errorf("failed to parse AI response: %w", err)
	}
	return nil
}

// TailorResume tailors a resume for a specific job description
func (c *Client) TailorResume(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
	// Get operation-specific configuration
	operationConfig, err := config.GetOperationConfig(config.OperationTailor)
	if err != nil {
		return nil, fmt.Errorf("failed to get tailor operation config: %w", err)
	}

	systemPrompt := SystemPrompts["tailor"]
	userPrompt := fmt.Sprintf(UserPrompts["tailor"], input.BaseResume, input.JobDescription)
	schema := c.buildTailorSchema()

	var output types.TailorResumeOutput
	err = c.generateJSON(ctx, systemPrompt, userPrompt, schema, &output, operationConfig, config.OperationTailor)
	if err != nil {
		return nil, fmt.Errorf("failed to tailor resume: %w", err)
	}

	return &output, nil
}

// EvaluateResume evaluates a tailored resume against the original
func (c *Client) EvaluateResume(ctx context.Context, input types.EvaluateResumeInput) (*types.EvaluateResumeOutput, error) {
	// Get operation-specific configuration
	operationConfig, err := config.GetOperationConfig(config.OperationEvaluate)
	if err != nil {
		return nil, fmt.Errorf("failed to get evaluate operation config: %w", err)
	}

	systemPrompt := SystemPrompts["evaluate"]
	userPrompt := fmt.Sprintf(UserPrompts["evaluate"], input.BaseResume, input.TailoredResume)
	schema := c.buildEvaluateSchema()

	var output types.EvaluateResumeOutput
	err = c.generateJSON(ctx, systemPrompt, userPrompt, schema, &output, operationConfig, config.OperationEvaluate)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate resume: %w", err)
	}

	return &output, nil
}

// AnalyzeJob analyzes a job description for quality and effectiveness
func (c *Client) AnalyzeJob(ctx context.Context, input types.AnalyzeJobInput) (*types.AnalyzeJobOutput, error) {
	// Get operation-specific configuration
	operationConfig, err := config.GetOperationConfig(config.OperationAnalyze)
	if err != nil {
		return nil, fmt.Errorf("failed to get analyze operation config: %w", err)
	}

	systemPrompt := SystemPrompts["analyze"]
	userPrompt := fmt.Sprintf(UserPrompts["analyze"], input.JobDescription)
	schema := c.buildAnalyzeSchema()

	var output types.AnalyzeJobOutput
	err = c.generateJSON(ctx, systemPrompt, userPrompt, schema, &output, operationConfig, config.OperationAnalyze)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze job: %w", err)
	}

	return &output, nil
}

// Close closes the AI client
func (c *Client) Close() error {
	// Note: genai.Client doesn't have a Close method in the current version
	// This is here for future compatibility and interface consistency
	return nil
}
