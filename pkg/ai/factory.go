package ai

import (
	"fmt"
	"sync"

	"resumatter/pkg/ai/providers"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// Re-export types from providers package
type Provider = providers.Provider
type GenerationRequest = providers.GenerationRequest
type GenerationResponse = providers.GenerationResponse
type GenerationConfig = providers.GenerationConfig
type ResponseSchema = providers.ResponseSchema
type UsageStats = providers.UsageStats

// ProviderFactory manages AI provider instances
type ProviderFactory struct {
	providers map[string]Provider
	configs   *config.ProviderConfig
	logger    logger.Logger
	mutex     sync.RWMutex
}

// NewProviderFactory creates a new provider factory
func NewProviderFactory(configs *config.ProviderConfig, logger logger.Logger) *ProviderFactory {
	return &ProviderFactory{
		providers: make(map[string]Provider),
		configs:   configs,
		logger:    logger,
	}
}

// GetProvider returns a provider instance, creating it if necessary
func (f *ProviderFactory) GetProvider(providerName string) (Provider, error) {
	f.mutex.RLock()
	if provider, exists := f.providers[providerName]; exists {
		f.mutex.RUnlock()
		return provider, nil
	}
	f.mutex.RUnlock()

	f.mutex.Lock()
	defer f.mutex.Unlock()

	// Double-check after acquiring write lock
	if provider, exists := f.providers[providerName]; exists {
		return provider, nil
	}

	provider, err := f.createProvider(providerName)
	if err != nil {
		return nil, err
	}

	f.providers[providerName] = provider
	return provider, nil
}

// createProvider creates a new provider instance
func (f *ProviderFactory) createProvider(name string) (Provider, error) {
	switch name {
	case "genai-gemini":
		return providers.NewGeminiAPIProvider(&f.configs.GeminiAPI, f.logger)
	case "genai-vertexai":
		return providers.NewVertexAIProvider(&f.configs.VertexAI, f.logger)
	case "openai":
		return providers.NewOpenAIProvider(&f.configs.OpenAI, f.logger)
	case "anthropic":
		return providers.NewAnthropicProvider(&f.configs.Anthropic, f.logger)
	default:
		return nil, fmt.Errorf("unsupported provider: %s", name)
	}
}

// Close closes all provider instances
func (f *ProviderFactory) Close() error {
	f.mutex.Lock()
	defer f.mutex.Unlock()

	var errs []error
	for name, provider := range f.providers {
		if err := provider.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close provider %s: %w", name, err))
		}
	}

	// Clear the providers map
	f.providers = make(map[string]Provider)

	if len(errs) > 0 {
		return errs[0] // Return first error
	}
	return nil
}

// ListProviders returns the names of all available providers
func (f *ProviderFactory) ListProviders() []string {
	return []string{"genai-gemini", "genai-vertexai", "openai", "anthropic"}
}
