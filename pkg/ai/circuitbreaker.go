package ai

import (
	"context"
	"fmt"

	"github.com/sony/gobreaker/v2"

	"resumatter/pkg/ai/providers"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
)

// AICircuitBreaker wraps AI operations with circuit breaker pattern
type AICircuitBreaker struct {
	cb     *gobreaker.CircuitBreaker[*providers.GenerationResponse]
	logger logger.Logger
}

// NewAICircuitBreaker creates a circuit breaker configured for a specific operation type
func NewAICircuitBreaker(operationType config.OperationType, cfg *config.ResolvedOperationConfig, log logger.Logger) *AICircuitBreaker {
	// If circuit breaker is disabled, return nil to indicate no circuit breaker
	if !cfg.CircuitBreakerEnabled {
		return nil
	}

	settings := gobreaker.Settings{
		Name:        fmt.Sprintf("AI-%s", string(operationType)),
		MaxRequests: cfg.CircuitBreakerMaxRequests,
		Interval:    cfg.CircuitBreakerInterval,
		Timeout:     cfg.CircuitBreakerTimeout,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= cfg.CircuitBreakerMinRequests &&
				failureRatio >= cfg.CircuitBreakerFailureThreshold
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			log.Info(context.Background(), "Circuit breaker state changed",
				logger.String("name", name),
				logger.String("operation_type", string(operationType)),
				logger.String("from", from.String()),
				logger.String("to", to.String()),
				logger.String("max_requests", fmt.Sprintf("%d", cfg.CircuitBreakerMaxRequests)),
				logger.String("failure_threshold", fmt.Sprintf("%.2f", cfg.CircuitBreakerFailureThreshold)))
		},
	}

	return &AICircuitBreaker{
		cb:     gobreaker.NewCircuitBreaker[*providers.GenerationResponse](settings),
		logger: log,
	}
}

// Execute executes the provided function with circuit breaker protection
func (cb *AICircuitBreaker) Execute(fn func() (*providers.GenerationResponse, error)) (*providers.GenerationResponse, error) {
	if cb == nil || cb.cb == nil {
		// If breaker is disabled/nil, just execute the function directly
		return fn()
	}
	return cb.cb.Execute(fn)
}

// GetStats returns circuit breaker statistics
func (cb *AICircuitBreaker) GetStats() map[string]any {
	if cb == nil || cb.cb == nil {
		return map[string]any{
			"enabled": false,
		}
	}

	return map[string]any{
		"name":    cb.cb.Name(),
		"state":   cb.cb.State().String(),
		"counts":  cb.cb.Counts(),
		"enabled": true,
	}
}

// IsHealthy returns true if the circuit breaker is in closed state
func (cb *AICircuitBreaker) IsHealthy() bool {
	if cb == nil || cb.cb == nil {
		return true // If no circuit breaker, consider it healthy
	}
	return cb.cb.State() == gobreaker.StateClosed
}

// GetState returns the current circuit breaker state
func (cb *AICircuitBreaker) GetState() string {
	if cb == nil || cb.cb == nil {
		return "disabled"
	}
	return cb.cb.State().String()
}

// GetCounts returns the current circuit breaker counts
func (cb *AICircuitBreaker) GetCounts() gobreaker.Counts {
	if cb == nil || cb.cb == nil {
		return gobreaker.Counts{}
	}
	return cb.cb.Counts()
}
