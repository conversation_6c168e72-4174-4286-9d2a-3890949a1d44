package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"resumatter/pkg/ai/providers"
	"resumatter/pkg/config"
	"resumatter/pkg/logger"
	"resumatter/pkg/types"
)

// Client handles AI operations using multiple providers
type Client struct {
	factory          *ProviderFactory
	config           *config.Config
	operationConfigs map[config.OperationType]*config.OperationAIConfig
	circuitBreakers  map[config.OperationType]*AICircuitBreaker
	logger           logger.Logger
}

// Make sure Client implements ClientInterface
var _ ClientInterface = (*Client)(nil)

// New creates a new AI client with multi-provider support
func New(cfg *config.Config, opts ...ClientOption) (*Client, error) {
	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	// Validate that all required operations are configured
	if err := cfg.ValidateOperationConfigs(); err != nil {
		return nil, fmt.Errorf("invalid operation configs: %w", err)
	}

	// Create logger
	loggerInstance := logger.NewDefault().Named("ai-client")

	// Create provider factory
	factory := NewProviderFactory(&cfg.Providers, loggerInstance)

	// Initialize client
	client := &Client{
		factory:          factory,
		config:           cfg,
		operationConfigs: make(map[config.OperationType]*config.OperationAIConfig),
		circuitBreakers:  make(map[config.OperationType]*AICircuitBreaker),
		logger:           loggerInstance,
	}

	// Load operation configurations
	for opName, opConfig := range cfg.AI.Operations {
		opType := config.OperationType(opName)

		// Apply defaults to operation config
		resolvedConfig, err := cfg.GetOperationConfig(opType)
		if err != nil {
			return nil, fmt.Errorf("failed to resolve config for operation %s: %w", opName, err)
		}

		client.operationConfigs[opType] = resolvedConfig

		// Create circuit breaker if enabled
		if opConfig.CircuitBreaker.Enabled {
			resolvedOpConfig, err := cfg.ResolveOperationConfig(opType)
			if err != nil {
				return nil, fmt.Errorf("failed to resolve operation config for circuit breaker %s: %w", opName, err)
			}

			cb := NewAICircuitBreaker(opType, resolvedOpConfig, loggerInstance)
			client.circuitBreakers[opType] = cb
		}
	}

	// Apply options
	for _, opt := range opts {
		opt(client)
	}

	return client, nil
}

// executeOperation executes an AI operation with the appropriate provider and circuit breaker
func (c *Client) executeOperation(ctx context.Context, opType config.OperationType, systemPrompt, userPrompt string, schema *providers.ResponseSchema, output any) error {
	// Get operation configuration
	opConfig, exists := c.operationConfigs[opType]
	if !exists {
		return fmt.Errorf("no configuration found for operation: %s", opType)
	}

	// Get provider
	provider, err := c.factory.GetProvider(opConfig.Provider)
	if err != nil {
		return fmt.Errorf("failed to get provider %s: %w", opConfig.Provider, err)
	}

	// Create generation config
	timeout, err := time.ParseDuration(opConfig.Timeout)
	if err != nil {
		return fmt.Errorf("invalid timeout for operation %s: %w", opType, err)
	}

	genConfig := &providers.GenerationConfig{
		Model:       opConfig.Model,
		Temperature: opConfig.Temperature,
		MaxTokens:   opConfig.MaxTokens,
		Timeout:     timeout,
	}

	// Create generation request
	request := &providers.GenerationRequest{
		SystemPrompt: systemPrompt,
		UserPrompt:   userPrompt,
		Schema:       schema,
		Config:       genConfig,
	}

	// Execute with circuit breaker if enabled
	var response *providers.GenerationResponse
	if cb, exists := c.circuitBreakers[opType]; exists {
		response, err = cb.Execute(func() (*providers.GenerationResponse, error) {
			return provider.GenerateStructuredContent(ctx, request)
		})
	} else {
		response, err = provider.GenerateStructuredContent(ctx, request)
	}

	if err != nil {
		return fmt.Errorf("failed to generate content with provider %s: %w", opConfig.Provider, err)
	}

	// Parse response
	if err := json.Unmarshal([]byte(response.Content), output); err != nil {
		return fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Log usage stats if available
	if response.Usage != nil {
		c.logger.Debug(ctx, "AI operation completed",
			logger.String("operation", string(opType)),
			logger.String("provider", opConfig.Provider),
			logger.String("model", opConfig.Model),
			logger.Int("prompt_tokens", response.Usage.PromptTokens),
			logger.Int("completion_tokens", response.Usage.CompletionTokens),
			logger.Int("total_tokens", response.Usage.TotalTokens),
		)
	}

	return nil
}

// TailorResume tailors a resume for a specific job description
func (c *Client) TailorResume(ctx context.Context, input types.TailorResumeInput) (*types.TailorResumeOutput, error) {
	systemPrompt := SystemPrompts["tailor"]
	userPrompt := fmt.Sprintf(UserPrompts["tailor"], input.BaseResume, input.JobDescription)
	schema := c.buildTailorSchema()

	var output types.TailorResumeOutput
	err := c.executeOperation(ctx, config.OperationTailor, systemPrompt, userPrompt, schema, &output)
	if err != nil {
		return nil, fmt.Errorf("failed to tailor resume: %w", err)
	}

	return &output, nil
}

// EvaluateResume evaluates a tailored resume against the original
func (c *Client) EvaluateResume(ctx context.Context, input types.EvaluateResumeInput) (*types.EvaluateResumeOutput, error) {
	systemPrompt := SystemPrompts["evaluate"]
	userPrompt := fmt.Sprintf(UserPrompts["evaluate"], input.BaseResume, input.TailoredResume)
	schema := c.buildEvaluateSchema()

	var output types.EvaluateResumeOutput
	err := c.executeOperation(ctx, config.OperationEvaluate, systemPrompt, userPrompt, schema, &output)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate resume: %w", err)
	}

	return &output, nil
}

// AnalyzeJob analyzes a job description for quality and effectiveness
func (c *Client) AnalyzeJob(ctx context.Context, input types.AnalyzeJobInput) (*types.AnalyzeJobOutput, error) {
	systemPrompt := SystemPrompts["analyze"]
	userPrompt := fmt.Sprintf(UserPrompts["analyze"], input.JobDescription)
	schema := c.buildAnalyzeSchema()

	var output types.AnalyzeJobOutput
	err := c.executeOperation(ctx, config.OperationAnalyze, systemPrompt, userPrompt, schema, &output)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze job: %w", err)
	}

	return &output, nil
}

// Close closes the AI client and all providers
func (c *Client) Close() error {
	if c.factory != nil {
		return c.factory.Close()
	}
	return nil
}
