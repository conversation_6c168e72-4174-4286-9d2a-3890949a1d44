package service

import (
	"context"

	"resumatter/pkg/logger"
	"resumatter/pkg/types"
)

// Option types for service methods
type TailorResumeOption func(*types.TailorResumeInput) error
type EvaluateResumeOption func(*types.EvaluateResumeInput) error
type AnalyzeJobOption func(*types.AnalyzeJobInput) error

// ServiceInterface defines the contract for the resume service
type ServiceInterface interface {
	TailorResume(ctx context.Context, opts ...TailorResumeOption) (*types.TailorResumeOutput, error)
	EvaluateResume(ctx context.Context, opts ...EvaluateResumeOption) (*types.EvaluateResumeOutput, error)
	AnalyzeJob(ctx context.Context, opts ...AnalyzeJobOption) (*types.AnalyzeJobOutput, error)
	Logger() logger.Logger
	Close() error
}
