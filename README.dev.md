# Reminders

## no emoji
emoji causes error in find-replace tool when editing files. Usually ends up in truncated/empty file.

## modernize: interface{} -> any
`map[string]interface{}` -> `map[string]any`
or simply
`modernize -fix -test ./...`
be careful with your edits after this, source code modified.

## format code
`go fmt ./...`
be careful with your edits after this, source code modified.

## check imports
list with diff
`goimports -d .`

autofix
`goimports -w .`
be careful with your edits after this, source code modified.

## lint
`golangci-lint run`
or
`make lint`


## quick reference access
`go doc pkgname.ExportedMember`

## package dependencies
`go get pkgname` instead of editing go.mod
or just use (import) it in code then `go mod tidy`

## gemini api key
`GEMINI_API_KEY=$(cat ./.key/gemini) cmd arg ...`

