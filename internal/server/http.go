package server

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sony/gobreaker/v2"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"

	"resumatter/pkg/logger"
	"resumatter/pkg/service"
	"resumatter/pkg/types"
)

// Config holds the HTTP server configuration
type Config struct {
	Port         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
	Environment  string // "development", "production"
}

// DefaultConfig returns default HTTP server configuration
func DefaultConfig() *Config {
	return &Config{
		Port:         "8080",
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
		Environment:  "development",
	}
}

// Server represents the HTTP server
type Server struct {
	config  *Config
	service service.ServiceInterface
	logger  logger.Logger
	server  *http.Server
	Router  *gin.Engine // Exported for testing
}

// NewServer creates a new HTTP server instance
func NewServer(config *Config, svc service.ServiceInterface) *Server {
	if config == nil {
		config = DefaultConfig()
	}

	// Set Gin mode based on environment
	if config.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	server := &Server{
		config:  config,
		service: svc,
		logger:  svc.Logger().Named("http-server"),
	}

	server.setupRouter()
	server.setupHTTPServer()

	return server
}

// setupRouter configures the Gin router with middleware and routes
func (s *Server) setupRouter() {
	s.Router = gin.New()

	// Add OpenTelemetry middleware for tracing
	s.Router.Use(otelgin.Middleware("resumatter-http"))

	// Add custom middleware
	s.Router.Use(s.loggingMiddleware())
	s.Router.Use(s.recoveryMiddleware())
	s.Router.Use(s.corsMiddleware())

	// Health check endpoints
	s.Router.GET("/health", s.healthCheck)
	s.Router.GET("/ready", s.readinessCheck)

	// API v1 routes
	v1 := s.Router.Group("/api/v1")
	{
		v1.POST("/tailor", s.tailorResume)
		v1.POST("/evaluate", s.evaluateResume)
		v1.POST("/analyze", s.analyzeJob)
	}
}

// setupHTTPServer configures the underlying HTTP server
func (s *Server) setupHTTPServer() {
	s.server = &http.Server{
		Addr:         ":" + s.config.Port,
		Handler:      s.Router,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
		IdleTimeout:  s.config.IdleTimeout,
	}
}

// Start starts the HTTP server
func (s *Server) Start() error {
	s.logger.Info(context.Background(), "Starting HTTP server",
		logger.String("port", s.config.Port),
		logger.String("environment", s.config.Environment))

	if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("failed to start HTTP server: %w", err)
	}

	return nil
}

// Shutdown gracefully shuts down the HTTP server
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info(ctx, "Shutting down HTTP server")

	if err := s.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("failed to shutdown HTTP server: %w", err)
	}

	// Close the underlying service
	if err := s.service.Close(); err != nil {
		s.logger.ErrorWithErr(ctx, "Failed to close service", err)
		return fmt.Errorf("failed to close service: %w", err)
	}

	s.logger.Info(ctx, "HTTP server shutdown complete")
	return nil
}

// Middleware implementations

// loggingMiddleware logs HTTP requests with structured logging
func (s *Server) loggingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// Process request
		c.Next()

		// Log after processing
		latency := time.Since(start)
		clientIP := c.ClientIP()
		method := c.Request.Method
		statusCode := c.Writer.Status()

		if raw != "" {
			path = path + "?" + raw
		}

		s.logger.Info(c.Request.Context(), "HTTP request processed",
			logger.String("method", method),
			logger.String("path", path),
			logger.Int("status", statusCode),
			logger.String("client_ip", clientIP),
			logger.String("latency", latency.String()),
			logger.Int("response_size", c.Writer.Size()))
	}
}

// recoveryMiddleware handles panics and returns 500 errors
func (s *Server) recoveryMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				s.logger.Error(c.Request.Context(), "Panic recovered in HTTP handler",
					logger.String("error", fmt.Sprintf("%v", err)),
					logger.String("method", c.Request.Method),
					logger.String("path", c.Request.URL.Path))

				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Internal server error",
				})
				c.Abort()
			}
		}()
		c.Next()
	}
}

// corsMiddleware handles CORS headers
func (s *Server) corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// Health check endpoints

// healthCheck returns the health status of the service
func (s *Server) healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now().UTC(),
		"service":   "resumatter",
	})
}

// readinessCheck returns the readiness status of the service
func (s *Server) readinessCheck(c *gin.Context) {
	// In a real implementation, you might check database connections, etc.
	c.JSON(http.StatusOK, gin.H{
		"status":    "ready",
		"timestamp": time.Now().UTC(),
		"service":   "resumatter",
	})
}

// API endpoint handlers

// tailorResume handles POST /api/v1/tailor
func (s *Server) tailorResume(c *gin.Context) {
	var input types.TailorResumeInput

	// Bind JSON input
	if err := c.ShouldBindJSON(&input); err != nil {
		s.logger.Error(c.Request.Context(), "Invalid JSON input for tailor request",
			logger.String("error", err.Error()))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid JSON input",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if input.BaseResume == "" || input.JobDescription == "" {
		s.logger.Error(c.Request.Context(), "Missing required fields for tailor request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Both baseResume and jobDescription are required",
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Processing tailor resume request",
		logger.Int("resume_length", len(input.BaseResume)),
		logger.Int("job_description_length", len(input.JobDescription)))

	// Call service layer
	result, err := s.service.TailorResume(c.Request.Context(),
		service.WithResumeData(input.BaseResume),
		service.WithJobDescriptionData(input.JobDescription))

	if err != nil {
		s.logger.ErrorWithErr(c.Request.Context(), "Failed to tailor resume", err)

		// Check if error is from circuit breaker being open
		if isCircuitBreakerOpenError(err) {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "Service temporarily unavailable",
				"details": "AI service is currently unavailable due to repeated failures",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to tailor resume",
			"details": err.Error(),
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Tailor resume request completed successfully",
		logger.Int("ats_score", result.ATSAnalysis.Score))

	c.JSON(http.StatusOK, result)
}

// evaluateResume handles POST /api/v1/evaluate
func (s *Server) evaluateResume(c *gin.Context) {
	var input types.EvaluateResumeInput

	// Bind JSON input
	if err := c.ShouldBindJSON(&input); err != nil {
		s.logger.Error(c.Request.Context(), "Invalid JSON input for evaluate request",
			logger.String("error", err.Error()))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid JSON input",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if input.BaseResume == "" || input.TailoredResume == "" {
		s.logger.Error(c.Request.Context(), "Missing required fields for evaluate request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Both baseResume and tailoredResume are required",
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Processing evaluate resume request",
		logger.Int("base_resume_length", len(input.BaseResume)),
		logger.Int("tailored_resume_length", len(input.TailoredResume)))

	// Call service layer
	result, err := s.service.EvaluateResume(c.Request.Context(),
		service.WithBaseResumeData(input.BaseResume),
		service.WithTailoredResumeData(input.TailoredResume))

	if err != nil {
		s.logger.ErrorWithErr(c.Request.Context(), "Failed to evaluate resume", err)

		// Check if error is from circuit breaker being open
		if isCircuitBreakerOpenError(err) {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "Service temporarily unavailable",
				"details": "AI service is currently unavailable due to repeated failures",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to evaluate resume",
			"details": err.Error(),
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Evaluate resume request completed successfully",
		logger.Int("findings_count", len(result.Findings)))

	c.JSON(http.StatusOK, result)
}

// analyzeJob handles POST /api/v1/analyze
func (s *Server) analyzeJob(c *gin.Context) {
	var input types.AnalyzeJobInput

	// Bind JSON input
	if err := c.ShouldBindJSON(&input); err != nil {
		s.logger.Error(c.Request.Context(), "Invalid JSON input for analyze request",
			logger.String("error", err.Error()))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid JSON input",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if input.JobDescription == "" {
		s.logger.Error(c.Request.Context(), "Missing required field for analyze request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "jobDescription is required",
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Processing analyze job request",
		logger.Int("job_description_length", len(input.JobDescription)))

	// Call service layer
	result, err := s.service.AnalyzeJob(c.Request.Context(),
		service.WithJobDescriptionDataForAnalysis(input.JobDescription))

	if err != nil {
		s.logger.ErrorWithErr(c.Request.Context(), "Failed to analyze job", err)

		// Check if error is from circuit breaker being open
		if isCircuitBreakerOpenError(err) {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error":   "Service temporarily unavailable",
				"details": "AI service is currently unavailable due to repeated failures",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to analyze job",
			"details": err.Error(),
		})
		return
	}

	s.logger.Info(c.Request.Context(), "Analyze job request completed successfully",
		logger.Int("job_quality_score", result.JobQualityScore))

	c.JSON(http.StatusOK, result)
}

// isCircuitBreakerOpenError checks if the error is from a circuit breaker being open
// Flow: gobreaker.CircuitBreaker.Execute() -> AICircuitBreaker.Execute() ->
//
//	client.generateJSON() -> service layer -> HTTP handler
//
// The gobreaker library returns ErrOpenState directly when circuit is open,
// and errors.Is() can detect it even when wrapped in multiple error layers
func isCircuitBreakerOpenError(err error) bool {
	return errors.Is(err, gobreaker.ErrOpenState)
}
