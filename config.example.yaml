# Resumatter Multi-Provider Configuration Example
# Copy this file to config.yaml and customize for your environment

# AI Configuration
ai:
  # Global defaults (fallback for operations without specific settings)
  default_provider: "genai-gemini"
  default_model: "gemini-2.0-flash-lite"
  default_temperature: 0.7
  default_timeout: "30s"

  # Per-operation configurations
  operations:
    # Resume tailoring - uses creative model with higher temperature
    tailor:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.7
      max_tokens: 8192
      timeout: "30s"
      api_key: "${GEMINI_API_KEY}"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 5
        max_requests: 3
        interval: "60s"
        timeout: "30s"

    # Resume evaluation - uses Vertex AI for enterprise reliability
    evaluate:
      provider: "genai-vertexai"
      model: "gemini-2.5-pro"
      temperature: 0.3
      max_tokens: 8192
      timeout: "30s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 5
        max_requests: 3
        interval: "60s"
        timeout: "30s"

    # Job analysis - balanced settings for analysis
    analyze:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.5
      max_tokens: 8192
      timeout: "30s"
      circuit_breaker:
        enabled: true
        failure_threshold: 0.6
        min_requests: 5
        max_requests: 3
        interval: "60s"
        timeout: "30s"

# Provider-specific configurations
providers:
  # Vertex AI configuration
  genai-vertexai:
    google_cloud_project: "${GOOGLE_CLOUD_PROJECT}"
    google_cloud_region: "us-central1"

# HTTP Server Configuration
server:
  port: "8080"
  environment: "development"

# Logging Configuration
logging:
  level: "info"
  format: "text"

# Observability Configuration
observability:
  tracing_enabled: false
  service_name: "resumatter"
  service_version: "dev"

# Environment Variable Examples:
# GEMINI_API_KEY=your_gemini_api_key
# GOOGLE_CLOUD_PROJECT=your-gcp-project
# GOOGLE_CLOUD_REGION=us-central1
# PORT=8080
# ENVIRONMENT=production
# LOG_LEVEL=info
# OTEL_TRACING_ENABLED=true