# Resumatter Configuration - Maintainers TODO
# This file contains configuration features that are promised in the example
# but not yet implemented. These are "broken promises" that need attention.

# ========================================================================
# CRITICAL PRIORITY - SECURITY ISSUE
# ========================================================================

# Authentication Configuration - BROKEN PROMISE
# Status: Configuration exists but NO implementation
# Risk: HIGH - Users may think API is protected when it's wide open
# Files to implement:
#   - internal/server/auth_middleware.go (create)
#   - Update internal/server/http.go setupRouter() to add auth middleware
auth:
  enabled: false  # Currently ignored - no middleware checks this
  logging_only: true  # Not implemented - should log auth attempts without blocking
  bearer_tokens:
    - token: "${AUTH_TOKEN_ADMIN}"
      permissions: ["read", "write", "admin"]  # Permission system not implemented
    - token: "${AUTH_TOKEN_USER}"
      permissions: ["read", "write"]
    - token: "${AUTH_TOKEN_READONLY}"
      permissions: ["read"]

# ========================================================================
# MEDIUM PRIORITY - PROVIDER IMPLEMENTATIONS
# ========================================================================

# Provider-specific configurations - PARTIALLY BROKEN
providers:
  # OpenAI configuration - BROKEN PROMISE
  # Status: Stub implementation returns "not yet implemented" error
  # Files to implement:
  #   - pkg/ai/providers/openai.go (complete implementation)
  #   - Add OpenAI SDK dependency
  openai:
    organization_id: "${OPENAI_ORG_ID}"
    base_url: "https://api.openai.com/v1"
  
  # Anthropic configuration - BROKEN PROMISE  
  # Status: Stub implementation returns "not yet implemented" error
  # Files to implement:
  #   - pkg/ai/providers/anthropic.go (complete implementation)
  #   - Add Anthropic SDK dependency
  anthropic:
    base_url: "https://api.anthropic.com"

# ========================================================================
# LOW PRIORITY - CONFIGURATION IMPROVEMENTS
# ========================================================================

# Provider base_url configurations - PARTIALLY BROKEN
# Status: Config exists but not fully utilized
# Current issues:
#   - genai-gemini base_url is defined but not used in client creation
#   - Should allow custom endpoints for testing/enterprise deployments
# Files to fix:
#   - pkg/ai/providers/genai-gemini.go (use base_url from config)
#   - pkg/ai/providers/genai-vertexai.go (consider base_url support)

# ========================================================================
# IMPLEMENTATION CHECKLIST
# ========================================================================

# [ ] CRITICAL: Implement authentication middleware
#     - Create bearer token validation
#     - Implement permission checking
#     - Add logging_only mode
#     - Add auth middleware to HTTP router
#     - Write tests for auth scenarios
#
# [ ] MEDIUM: Implement OpenAI provider
#     - Add OpenAI Go SDK dependency
#     - Implement GenerateStructuredContent method
#     - Handle organization_id and base_url configs
#     - Add proper error handling and retries
#     - Write provider tests
#
# [ ] MEDIUM: Implement Anthropic provider  
#     - Add Anthropic Go SDK dependency
#     - Implement GenerateStructuredContent method
#     - Handle base_url config
#     - Add proper error handling and retries
#     - Write provider tests
#
# [ ] LOW: Fix provider base_url usage
#     - Update Gemini provider to use base_url config
#     - Consider adding base_url support to Vertex AI
#     - Update provider factory to pass configs properly
#
# [ ] TESTING: Add integration tests
#     - Test auth middleware with various token scenarios
#     - Test provider switching and fallbacks
#     - Test configuration validation edge cases

# ========================================================================
# NOTES FOR MAINTAINERS
# ========================================================================

# 1. The authentication system is the highest priority as it's a security
#    concern. Users seeing auth config may assume the API is protected.
#
# 2. OpenAI and Anthropic providers are marked as "future use" in the 
#    example but should either be implemented or removed from the config.
#
# 3. Consider adding feature flags to disable unimplemented providers
#    rather than returning "not implemented" errors at runtime.
#
# 4. The circuit breaker and observability features are fully implemented
#    and working correctly - these are good examples to follow.
