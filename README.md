# Resumatter

An AI-powered HTTP service for resume optimization and job analysis. Built on Google's GenAI platform with configurable per-operation models, circuit breaker protection, structured logging, and OpenTelemetry tracing.

## Features

- **Resume tailoring**: Adapt resume content for specific job postings using AI
- **Resume evaluation**: Check consistency and quality between different resume versions
- **Job analysis**: Review job descriptions for clarity, completeness, and effectiveness
- **Google GenAI support**: Gemini API and Vertex AI with configurable models per operation
- **Per-operation configuration**: Different models, temperatures, and settings for each AI operation
- **Circuit breaker protection**: Automatic failover and recovery for AI service reliability
- **Observability**: Structured logging and OpenTelemetry tracing support
- **RESTful API**: JSON-based HTTP endpoints for easy integration

## Requirements

- Go 1.24.5 or later
- Google Gemini API key or Google Cloud Project (for Vertex AI)

## Quick Start

1. **Get the code and build:**
   ```bash
   git clone <repository>
   cd resumatter
   make build
   ```

2. **Configure the service:**
   ```bash
   # Copy example configuration
   cp config.example.yaml config.yaml

   # Set your API key
   export GEMINI_API_KEY="your-api-key"
   # OR for Vertex AI
   export GOOGLE_CLOUD_PROJECT="your-project-id"
   ```

3. **Start the server:**
   ```bash
   ./build/resumatter
   # Server starts on http://localhost:8080
   ```

## API Usage

### Tailor Resume
```bash
curl -X POST http://localhost:8080/api/v1/tailor \
  -H "Content-Type: application/json" \
  -d '{
    "baseResume": "Your resume content here...",
    "jobDescription": "Job posting content here..."
  }'
```

### Evaluate Resume
```bash
curl -X POST http://localhost:8080/api/v1/evaluate \
  -H "Content-Type: application/json" \
  -d '{
    "baseResume": "Original resume content...",
    "tailoredResume": "Modified resume content..."
  }'
```

### Analyze Job Description
```bash
curl -X POST http://localhost:8080/api/v1/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "jobDescription": "Job posting to analyze..."
  }'
```

### Health Checks
```bash
# Health check
curl http://localhost:8080/health

# Readiness check
curl http://localhost:8080/ready
```

## Configuration

The service uses YAML configuration with environment variable support. See `config.example.yaml` for all available options:

- **Per-operation AI settings**: Configure different models, temperatures, and parameters for each operation (tailor, evaluate, analyze)
- **Google GenAI providers**: Support for both Gemini API (direct) and Vertex AI (Google Cloud)
- **Circuit Breakers**: Failure thresholds, timeouts, and recovery settings per operation
- **Server**: Port, environment, and HTTP timeouts
- **Logging**: Level and format (text/json)
- **Observability**: OpenTelemetry tracing configuration

### Example Configuration
```yaml
ai:
  operations:
    # Use creative model for resume tailoring
    tailor:
      provider: "genai-gemini"
      model: "gemini-2.0-flash"
      temperature: 0.7

    # Use enterprise Vertex AI for evaluation
    evaluate:
      provider: "genai-vertexai"
      model: "gemini-2.0-flash-lite"
      temperature: 0.3
```

## Getting API Keys

- **Google Gemini API**: Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
- **Google Vertex AI**: Set up a [Google Cloud Project](https://cloud.google.com/vertex-ai/docs/start/cloud-environment)

## Development

```bash
# Run tests
make test

# Lint code
make lint

# Format code
make fmt
```

## License

Apache License 2.0 - see [LICENSE](LICENSE) file.
