# Resumatter

A multi-provider AI HTTP service for resume optimization and job analysis. Supports Google Gemini API and Vertex AI with circuit breaker protection, structured logging, and OpenTelemetry tracing.

## Features

- **Resume tailoring**: Adapt resume content for specific job postings using AI
- **Resume evaluation**: Check consistency and quality between different resume versions
- **Job analysis**: Review job descriptions for clarity, completeness, and effectiveness
- **Multi-provider support**: Google Gemini API and Vertex AI (OpenAI/Anthropic planned)
- **Circuit breaker protection**: Automatic failover and recovery for AI service reliability
- **Observability**: Structured logging and OpenTelemetry tracing support
- **RESTful API**: JSON-based HTTP endpoints for easy integration

## Requirements

- Go 1.24.5 or later
- Google Gemini API key or Google Cloud Project (for Vertex AI)

## Quick Start

1. **Get the code and build:**
   ```bash
   git clone <repository>
   cd resumatter
   make build
   ```

2. **Configure the service:**
   ```bash
   # Copy example configuration
   cp config.example.yaml config.yaml

   # Set your API key
   export GEMINI_API_KEY="your-api-key"
   # OR for Vertex AI
   export GOOGLE_CLOUD_PROJECT="your-project-id"
   ```

3. **Start the server:**
   ```bash
   ./build/resumatter
   # Server starts on http://localhost:8080
   ```

## API Usage

### Tailor Resume
```bash
curl -X POST http://localhost:8080/api/v1/tailor \
  -H "Content-Type: application/json" \
  -d '{
    "baseResume": "Your resume content here...",
    "jobDescription": "Job posting content here..."
  }'
```

### Evaluate Resume
```bash
curl -X POST http://localhost:8080/api/v1/evaluate \
  -H "Content-Type: application/json" \
  -d '{
    "baseResume": "Original resume content...",
    "tailoredResume": "Modified resume content..."
  }'
```

### Analyze Job Description
```bash
curl -X POST http://localhost:8080/api/v1/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "jobDescription": "Job posting to analyze..."
  }'
```

### Health Checks
```bash
# Health check
curl http://localhost:8080/health

# Readiness check
curl http://localhost:8080/ready
```

## Configuration

The service uses YAML configuration with environment variable support. See `config.example.yaml` for all available options:

- **AI Providers**: Configure Gemini API, Vertex AI, and operation-specific settings
- **Circuit Breakers**: Failure thresholds, timeouts, and recovery settings
- **Server**: Port, environment, and HTTP timeouts
- **Logging**: Level and format (text/json)
- **Observability**: OpenTelemetry tracing configuration

## Getting API Keys

- **Google Gemini API**: Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
- **Google Vertex AI**: Set up a [Google Cloud Project](https://cloud.google.com/vertex-ai/docs/start/cloud-environment)

## Development

```bash
# Run tests
make test

# Run with live reload (requires air)
make dev

# Lint code
make lint

# Format code
make fmt
```

## License

Apache License 2.0 - see [LICENSE](LICENSE) file.
