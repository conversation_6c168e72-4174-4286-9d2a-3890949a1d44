# Resumatter

A simple command-line tool that uses AI to help with resume and job description tasks.

## What it does

- **Resume tailoring**: Adapt resume content for specific job postings
- **Resume evaluation**: Check for consistency between different resume versions
- **Job analysis**: Review job descriptions for clarity and quality

## Requirements

- Go 1.24.5 or later
- Google Gemini API key

1. Get the code and build:
   ```bash
   git clone <repository>
   cd resumatter
   make build
   ```

2. Set your API key:
   ```bash
   export GEMINI_API_KEY="your-api-key"
   ```

## Usage

```bash
# Tailor a resume for a job
./build/resumatter tailor resume.txt job.txt

# Check consistency between resumes  
./build/resumatter evaluate original.txt tailored.txt

# Analyze a job posting
./build/resumatter analyze job.txt
```

Add `--format json` for JSON output or `-o filename` to save results.

## Getting an API Key

Visit [Google AI Studio](https://aistudio.google.com/app/apikey) to create a free API key.

## License

Apache License 2.0 - see [LICENSE](LICENSE) file.
