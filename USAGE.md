# Resumatter - Usage Guide

## Quick Start

1. **Set your API key:**
   ```bash
   export GEMINI_API_KEY="your-api-key-here"
   ```

2. **Build the application:**
   ```bash
   make build
   # or
   go build -o resumatter .
   ```

3. **Run examples:**
   ```bash
   # Tailor a resume
   ./resumatter tailor examples/resume.txt examples/job.txt
   
   # Evaluate for accuracy
   ./resumatter evaluate examples/resume.txt examples/resume.txt
   
   # Analyze job quality
   ./resumatter analyze examples/job.txt
   ```

## Commands

### 1. Tailor Resume
```bash
./resumatter tailor [resume-file] [job-description-file]
```

**Purpose:** Optimizes a resume for a specific job posting using AI.

**Output includes:**
- Tailored resume text
- ATS compatibility score (0-100)
- Job posting quality analysis

**Example:**
```bash
./resumatter tailor examples/resume.txt examples/job.txt --format json -o tailored.json
```

### 2. Evaluate Resume
```bash
./resumatter evaluate [base-resume] [tailored-resume]
```

**Purpose:** Checks tailored resume for fabrications, exaggerations, or inconsistencies.

**Detects:**
- **Overclaims:** Exaggerated content
- **Inventions:** Completely fabricated information  
- **Incorrect Linking:** Misattributed skills/achievements

**Example:**
```bash
./resumatter evaluate examples/resume.txt tailored-resume.txt
```

### 3. Analyze Job Description
```bash
./resumatter analyze [job-description-file]
```

**Purpose:** Assesses job posting quality, clarity, and inclusivity.

**Analysis includes:**
- Overall quality score (0-100)
- Clarity assessment with improvements
- Inclusivity analysis with flagged terms
- Candidate attraction factors
- Market competitiveness evaluation
- Actionable recommendations

**Example:**
```bash
./resumatter analyze examples/job.txt --format json
```

## Output Formats

### Text Format (Default)
Human-readable format with clear sections and formatting.

### JSON Format
Structured data perfect for integration with other tools:
```bash
--format json
```

## Global Options

- `-o, --output FILE`: Save output to file instead of stdout
- `--format FORMAT`: Choose output format (text or json)

## Environment Variables

- `GEMINI_API_KEY`: Your Google Gemini API key (required)
- `RESUMATTER_AI_APIKEY`: Alternative API key variable name
- `GEMINI_MODEL`: AI model to use (default: "gemini-2.0-flash")

## Make Targets

```bash
make build          # Build the binary
make clean          # Clean build artifacts
make deps           # Download dependencies
make run-example    # Run tailor example
make run-evaluate   # Run evaluation example
make run-analyze    # Run analysis example
make install        # Install to $GOPATH/bin
make help           # Show all targets
```

## Error Handling

The tool provides clear error messages for:
- Missing API key
- Invalid file paths
- Network connectivity issues
- AI service errors
- Invalid output formats

## Tips

1. **File formats:** Use plain text files (.txt) for best results
2. **API limits:** Be mindful of Gemini API rate limits and costs
3. **Output:** Use JSON format for automation and integration
4. **Privacy:** Files are sent to Google's Gemini API for processing